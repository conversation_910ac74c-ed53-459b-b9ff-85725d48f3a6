Shader "URP/MowGrass/CS_MowGrass"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" { }
        _TopColor ("TopColor", color) = (1, 1, 1, 1)
        _BottomColor ("BottomColor", color) = (1, 1, 1, 1)
        
        _NoiseTex ("NoiseTex", 2D) = "white" { }
        _NoiseIntensity ("NoiseIntensity", float) = 1
        _NoiseSpeed ("NoiseSpeed", float) = 1
        
        _GrassPlayerWave ("GrassPlayerWave", float) = 2
        
        _WaveSinColor("WaveSinColor",color) = (1,1,1,1)
        _WaveA("WaveA",float) = 1
        _WaveL("WaveL",float) = 1
        _WaveS("WaveS",float) = 1
    }
    SubShader
    {
        Tags 
        { 
            "RenderType" = "Opaque" 
            "RenderPipeline" = "UniversalPipeline"
            "Queue" = "Geometry"
        }

        Pass
        {
            Name "ForwardLit"
            Tags { "LightMode" = "UniversalForward" }
            
            Cull Off
            
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            // URP keywords
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE
            #pragma multi_compile _ _SHADOWS_SOFT
            #pragma multi_compile_fragment _ _SCREEN_SPACE_OCCLUSION

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
          

            struct Attributes
            {
                float4 positionOS: POSITION;
                float2 uv: TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS: SV_POSITION;
                float2 uv: TEXCOORD0;
                float2 ndcUV: TEXCOORD1;
                float sinF: TEXCOORD2;
                float3 positionWS: TEXCOORD3;
            };
            
            struct DrawVertex
            {
                float4x4 mat;
                float2 uv;
                float2 sourceUV;
            };
            
            StructuredBuffer<DrawVertex> _DrawTriangles;

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            
            TEXTURE2D(_NoiseTex);
            SAMPLER(sampler_NoiseTex);
            float4 _NoiseTex_ST;
            
            TEXTURE2D(_MowGrassTex);
            SAMPLER(sampler_MowGrassTex);
            float4x4 _MowGrassMatrix;
            
            TEXTURE2D(_MowWaveGrassTex);
            SAMPLER(sampler_MowWaveGrassTex);
            float4x4 _MowWaveGrassMatrix;
            
            CBUFFER_START(UnityPerMaterial)
                half4 _TopColor;
                half4 _BottomColor;
                float _NoiseSpeed;
                float _NoiseIntensity;
                float _GrassPlayerWave;
                float3 _PlayerForward;
                half4 _WaveSinColor;
                float _WaveA;
                float _WaveL;
                float _WaveS;
            CBUFFER_END
            
            float2 GetUV(float4x4 matrixTemp, float4 pos)
            {
                float4 ndcpos = mul(matrixTemp, pos);
                ndcpos.xyz = ndcpos.xyz / ndcpos.w;
                float3 uvpos = ndcpos * 0.5 + 0.5;
                float2 uv = uvpos.xy;
                return uv;
            }

            float3 GetNoiseDelta(float2 sourceUV, float2 uv)
            {
                float2 noise_UV = TRANSFORM_TEX(sourceUV, _NoiseTex);
                half4 noiseColor = SAMPLE_TEXTURE2D_LOD(_NoiseTex, sampler_NoiseTex, noise_UV + float2(_Time.y * _NoiseSpeed, 0), 0);
                float noise = noiseColor.r * _NoiseIntensity * uv.y;
                float3 noiseDelta = float3(noise, noise, 0);
                return noiseDelta;
            }

            float3 GetWaveDelta(float3 pos, float2 uv, float3 worldPos)
            {
                float2 mowWaveGrassUV = GetUV(_MowWaveGrassMatrix, float4(pos, 1));
                half4 mowWaveGrassColor = SAMPLE_TEXTURE2D_LOD(_MowWaveGrassTex, sampler_MowWaveGrassTex, float2(mowWaveGrassUV.x, mowWaveGrassUV.y), 0);
                float mowWave = mowWaveGrassColor.g * _GrassPlayerWave * uv.y;
                float3 direction = normalize(worldPos - _PlayerForward);
                float3 mowWaveDelta = direction * mowWave;
                return mowWaveDelta;
            }

            float GetSinF(float3 worldPos)
            {
                float w = 2 * PI / _WaveL;
                float f = w * _Time.y * _WaveS;
                float a = _WaveA;
                float sinF = a * sin(worldPos.z * w + f);
                return sinF;
            }

            float3 GetSinWave(float sinF, float2 uv)
            {
                float temp = sinF * uv.y;
                return float3(0, temp, 0);
            }
            
            Varyings vert(Attributes input, uint vertexID: SV_InstanceID)
            {
                Varyings output;
                
                DrawVertex drawInput = _DrawTriangles[vertexID];

                float2 uv = input.uv;
                float3 positionWS = float3(drawInput.mat[0][3], drawInput.mat[1][3], drawInput.mat[2][3]);
                float sinF = GetSinF(positionWS);

                input.positionOS = mul(drawInput.mat, input.positionOS);
                input.positionOS.xyz += GetNoiseDelta(drawInput.sourceUV, uv)
                            + GetWaveDelta(positionWS, uv, positionWS)
                            + GetSinWave(sinF, uv);
                input.positionOS.w = 1;
                
                output.positionCS = mul(UNITY_MATRIX_VP, input.positionOS);
                output.positionWS = input.positionOS.xyz;
                output.uv = uv;
                output.ndcUV = GetUV(_MowGrassMatrix, float4(positionWS, 1));
                output.sinF = sinF;
                
                return output;
            }

            half4 frag(Varyings input): SV_Target
            {
                half4 grassCol = SAMPLE_TEXTURE2D(_MowGrassTex, sampler_MowGrassTex, input.ndcUV);
                
                if (grassCol.r == 1)
                {
                    discard;
                }

                half4 finalColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.ndcUV);
                half4 sinColor = _WaveSinColor * input.sinF;
                
                return half4(lerp(finalColor.rgb * _TopColor.rgb + sinColor.rgb, finalColor.rgb * _BottomColor.rgb, 1 - input.uv.y), 1);
            }
            ENDHLSL
        }
        
        
        // Shadow Caster Pass for URP
        Pass
        {
            Name "ShadowCaster"
            Tags { "LightMode" = "ShadowCaster" }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull Off

            HLSLPROGRAM
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"

            struct Attributes
            {
                float4 positionOS: POSITION;
                float2 uv: TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS: SV_POSITION;
                float2 ndcUV: TEXCOORD0;
            };

            struct DrawVertex
            {
                float4x4 mat;
                float2 uv;
                float2 sourceUV;
            };
            
            StructuredBuffer<DrawVertex> _DrawTriangles;
            
            TEXTURE2D(_NoiseTex);
            SAMPLER(sampler_NoiseTex);
            float4 _NoiseTex_ST;
            
            TEXTURE2D(_MowGrassTex);
            SAMPLER(sampler_MowGrassTex);
            float4x4 _MowGrassMatrix;
            
            TEXTURE2D(_MowWaveGrassTex);
            SAMPLER(sampler_MowWaveGrassTex);
            float4x4 _MowWaveGrassMatrix;
            
            CBUFFER_START(UnityPerMaterial)
                float _NoiseSpeed;
                float _NoiseIntensity;
                float _GrassPlayerWave;
                float3 _PlayerForward;
                float _WaveA;
                float _WaveL;
                float _WaveS;
            CBUFFER_END

            float2 GetUV(float4x4 matrixTemp, float4 pos)
            {
                float4 ndcpos = mul(matrixTemp, pos);
                ndcpos.xyz = ndcpos.xyz / ndcpos.w;
                float3 uvpos = ndcpos * 0.5 + 0.5;
                float2 uv = uvpos.xy;
                return uv;
            }

            float3 GetNoiseDelta(float2 sourceUV, float2 uv)
            {
                float2 noise_UV = TRANSFORM_TEX(sourceUV, _NoiseTex);
                half4 noiseColor = SAMPLE_TEXTURE2D_LOD(_NoiseTex, sampler_NoiseTex, noise_UV + float2(_Time.y * _NoiseSpeed, 0), 0);
                float noise = noiseColor.r * _NoiseIntensity * uv.y;
                float3 noiseDelta = float3(noise, noise, 0);
                return noiseDelta;
            }

            float3 GetWaveDelta(float3 pos, float2 uv, float3 worldPos)
            {
                float2 mowWaveGrassUV = GetUV(_MowWaveGrassMatrix, float4(pos, 1));
                half4 mowWaveGrassColor = SAMPLE_TEXTURE2D_LOD(_MowWaveGrassTex, sampler_MowWaveGrassTex, float2(mowWaveGrassUV.x, mowWaveGrassUV.y), 0);
                float mowWave = mowWaveGrassColor.g * _GrassPlayerWave * uv.y;
                float3 direction = normalize(worldPos - _PlayerForward);
                float3 mowWaveDelta = direction * mowWave;
                return mowWaveDelta;
            }

            float GetSinF(float3 worldPos)
            {
                float w = 2 * PI / _WaveL;
                float f = w * _Time.y * _WaveS;
                float a = _WaveA;
                float sinF = a * sin(worldPos.z * w + f);
                return sinF;
            }

            float3 GetSinWave(float sinF, float2 uv)
            {
                float temp = sinF * uv.y;
                return float3(0, temp, 0);
            }

            Varyings ShadowPassVertex(Attributes input, uint vertexID: SV_InstanceID)
            {
                Varyings output;
                
                DrawVertex drawInput = _DrawTriangles[vertexID];

                float2 uv = input.uv;
                float3 positionWS = float3(drawInput.mat[0][3], drawInput.mat[1][3], drawInput.mat[2][3]);
                float sinF = GetSinF(positionWS);

                input.positionOS = mul(drawInput.mat, input.positionOS);
                input.positionOS.xyz += GetNoiseDelta(drawInput.sourceUV, uv)
                            + GetWaveDelta(positionWS, uv, positionWS)
                            + GetSinWave(sinF, uv);
                input.positionOS.w = 1;
                
                output.positionCS = GetShadowPositionHClip(input.positionOS.xyz, float3(0,1,0));
                output.ndcUV = GetUV(_MowGrassMatrix, float4(positionWS, 1));
                
                return output;
            }

            half4 ShadowPassFragment(Varyings input): SV_Target
            {
                half4 grassCol = SAMPLE_TEXTURE2D(_MowGrassTex, sampler_MowGrassTex, input.ndcUV);
                
                if (grassCol.r == 1)
                {
                    discard;
                }
                
                return 0;
            }
            ENDHLSL
        }
    }
}
