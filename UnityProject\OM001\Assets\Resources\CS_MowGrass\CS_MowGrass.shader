Shader "SC/MowGrass/CS_MowGrass"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" { }
        _TopColor ("TopColor", color) = (1, 1, 1, 1)
        _BottomColor ("BottomColor", color) = (1, 1, 1, 1)
        
        _NoiseTex ("NoiseTex", 2D) = "white" { }
        _NoiseIntensity ("NoiseIntensity", float) = 1
        _NoiseSpeed ("NoiseSpeed", float) = 1
        
        _GrassPlayerWave ("GrassPlayerWave", float) = 2
        
        _WaveSinColor("WaveSinColor",color) = (1,1,1,1)
        _WaveA("WaveA",float) = 1
        _WaveL("WaveL",float) = 1
        _WaveS("WaveS",float) = 1
    }
    SubShader
    {
        Tags { "RenderType" = "Opaque" }

        Pass
        {
            Cull Off
            CGPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex: POSITION;
                float2 uv:TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex: SV_POSITION;
                float2 uv: TEXCOORD0;
                float2 ndcUV: TEXCOORD1;
                float sinF: TEXCOORD2;
            };
            
            struct DrawVertex
            {
                float4x4 mat;
                float2 uv;
                float2 sourceUV;
            };
            
            StructuredBuffer<DrawVertex> _DrawTriangles;

            sampler2D _MainTex;
            fixed4 _TopColor;
            fixed4 _BottomColor;
            
            sampler2D _NoiseTex;
            float4 _NoiseTex_ST;
            float _NoiseSpeed;
            float _NoiseIntensity;
            
            sampler2D _MowGrassTex;
            float4x4 _MowGrassMatrix;
            
            sampler2D _MowWaveGrassTex;
            float4x4 _MowWaveGrassMatrix;
            float _GrassPlayerWave;
            float3 _PlayerForward;

            fixed4 _WaveSinColor;
            float _WaveA;
            float _WaveL;
            float _WaveS;
            
            float2 GetUV(float4x4 matrixTemp, float4 pos)
            {
                float4 ndcpos = mul(matrixTemp, pos);
                ndcpos.xyz = ndcpos.xyz / ndcpos.w;
                float3 uvpos = ndcpos * 0.5 + 0.5;
                float2 uv = uvpos.xy;
                return uv;
            }

            float3 GetNoiseDelta(float2 sourceUV, float2 uv)
            {
                float2 noise_UV = TRANSFORM_TEX(sourceUV, _NoiseTex);
                fixed4 noiseColor = tex2Dlod(_NoiseTex, float4(noise_UV + float2(_Time.y * _NoiseSpeed, 0), 0, 0));
                float noise = noiseColor.r * _NoiseIntensity * uv.y;
                float3 noiseDelta = float3(noise, noise, 0);
                return noiseDelta;
            }

            float3 GetWaveDelta(float3 pos, float2 uv,float3 worldPos)
            {
                float2 mowWaveGrassUV = GetUV(_MowWaveGrassMatrix, float4(pos, 1));
                fixed4 mowWaveGrassColor = tex2Dlod(_MowWaveGrassTex, float4(float2(mowWaveGrassUV.x, mowWaveGrassUV.y), 0, 0));
                float mowWave = mowWaveGrassColor.g * _GrassPlayerWave * uv.y;
                float3 direction=normalize(worldPos-_PlayerForward);
                float3 mowWaveDelta = direction * mowWave;
                return mowWaveDelta;
            }

            float GetSinF(float3 worldPos)
            {
                float w = 2 * UNITY_PI / _WaveL;
                float f = w * _Time.y * _WaveS;
                float a = _WaveA;
                float sinF = a * sin(worldPos.z * w + f);
                return sinF;
            }

            float3 GetSinWave(float sinF, float2 uv)
            {
                float temp = sinF * uv.y;
                return float3(0,temp,0);
            }
            
            v2f vert(appdata v, uint vertexID: SV_InstanceID)
            {
                v2f o;
                
                DrawVertex input = _DrawTriangles[vertexID];

                float2 uv = v.uv;
                float3 positionWS = float3(input.mat[0][3], input.mat[1][3], input.mat[2][3]);
                float sinF = GetSinF(positionWS);

                
                v.vertex = mul(input.mat,v.vertex);
                v.vertex.xyz += GetNoiseDelta(input.sourceUV, uv)
                            + GetWaveDelta(positionWS, uv,positionWS)
                            + GetSinWave(sinF, uv);
                v.vertex.w = 1;
                
                //v.vertex.xyz += input.positionWS.xyz;
                //o.vertex = UnityObjectToClipPos(v.vertex);

                o.vertex = mul(unity_MatrixVP,v.vertex);
                o.uv = uv;
                o.ndcUV = GetUV(_MowGrassMatrix, float4(positionWS, 1));
                o.sinF = sinF;
                
                return o;
            }

            fixed4 frag(v2f i): SV_Target
            {
                fixed4 grassCol = tex2D(_MowGrassTex, i.ndcUV);
                
                if (grassCol.r == 1)
                {
                    discard;
                }

                fixed4 finalColor = tex2D(_MainTex, i.ndcUV);
                fixed4 sinColor = _WaveSinColor * i.sinF;
                
                return fixed4(lerp(finalColor.rgb * _TopColor.rgb + sinColor.rgb, finalColor.rgb * _BottomColor.rgb, 1 - i.uv.y), 1);
            }
            ENDCG
            
        }
    }
}
